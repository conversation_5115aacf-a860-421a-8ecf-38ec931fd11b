package com.fasnote.alm.checklist.controller;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fasnote.alm.checklist.dto.ApiResponse;
import com.fasnote.alm.checklist.dto.ResponseBuilder;
import com.fasnote.alm.checklist.model.ChecklistReview;
import com.fasnote.alm.checklist.model.ReviewRecord;

import com.fasnote.alm.checklist.model.ReviewTemplateVersion;
import com.fasnote.alm.checklist.model.TemplateSnapshot;
import com.fasnote.alm.checklist.service.ChecklistReviewService;

/**
 * 检查单评审管理控制器
 * 提供评审管理的 REST API 接口
 */
@RestController
@RequestMapping("/reviews")
public class ChecklistReviewController {

    private final ChecklistReviewService reviewService;

    public ChecklistReviewController(ChecklistReviewService reviewService) {
        this.reviewService = reviewService;
    }

    /**
     * 创建评审实例
     * POST /api/reviews?projectId=xxx
     */
    @PostMapping
    public ResponseEntity<ApiResponse<ChecklistReview>> createReview(
            @RequestParam String projectId,
            @RequestBody Map<String, String> request) {
        try {
            // 参数验证
            if (projectId == null || projectId.trim().isEmpty()) {
                return ResponseBuilder.badRequest("项目ID不能为空");
            }

            String templateId = request.get("templateId");
            if (templateId == null || templateId.trim().isEmpty()) {
                return ResponseBuilder.badRequest("模板ID不能为空");
            }

            String reviewId = request.get("id");
            ChecklistReview review;

            if (reviewId != null && !reviewId.trim().isEmpty()) {
                // 如果指定了ID，使用指定ID创建评审
                review = reviewService.createReviewFromTemplateWithId(projectId.trim(), templateId.trim(), reviewId.trim());
            } else {
                // 否则使用自动生成的ID
                review = reviewService.createReviewFromTemplate(projectId.trim(), templateId.trim());
            }

            return ResponseBuilder.created(review);
        } catch (IllegalArgumentException e) {
            return ResponseBuilder.validationError(e.getMessage());
        } catch (IOException e) {
            return ResponseBuilder.error("REVIEW_CREATE_ERROR", "创建评审实例失败: " + e.getMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("UNKNOWN_ERROR", "创建评审实例时发生未知错误: " + e.getMessage());
        }
    }

    /**
     * 获取评审实例
     * GET /api/reviews/{id}?projectId=xxx
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<ChecklistReview>> getReviewById(
            @PathVariable String id,
            @RequestParam String projectId) {
        try {
            // 参数验证
            if (id == null || id.trim().isEmpty()) {
                return ResponseBuilder.badRequest("评审ID不能为空");
            }
            if (projectId == null || projectId.trim().isEmpty()) {
                return ResponseBuilder.badRequest("项目ID不能为空");
            }

            Optional<ChecklistReview> reviewOpt = reviewService.getReviewById(projectId.trim(), id.trim());
            if (reviewOpt.isPresent()) {
                return ResponseBuilder.success(reviewOpt.get());
            } else {
                return ResponseBuilder.notFound("评审实例不存在: " + id);
            }
        } catch (IOException e) {
            return ResponseBuilder.error("REVIEW_READ_ERROR", "读取评审实例失败: " + e.getMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("UNKNOWN_ERROR", "获取评审实例时发生未知错误: " + e.getMessage());
        }
    }

    /**
     * 根据类型获取评审列表
     * GET /api/reviews?projectId=xxx&type={type}
     */
    @GetMapping
    public ResponseEntity<ApiResponse<List<ChecklistReview>>> getReviews(
            @RequestParam String projectId,
            @RequestParam(required = false) String type) {
        try {
            // 参数验证
            if (projectId == null || projectId.trim().isEmpty()) {
                return ResponseBuilder.badRequest("项目ID不能为空");
            }

            List<ChecklistReview> reviews;
            if (type != null && !type.trim().isEmpty()) {
                reviews = reviewService.getReviewsByType(projectId.trim(), type.trim());
            } else {
                // 如果没有指定类型，返回最近的评审列表
                reviews = reviewService.getRecentReviews(projectId.trim(), 50);
            }

            return ResponseBuilder.success(reviews);
        } catch (IllegalArgumentException e) {
            return ResponseBuilder.validationError(e.getMessage());
        } catch (IOException e) {
            return ResponseBuilder.error("REVIEW_READ_ERROR", "读取评审列表失败: " + e.getMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("UNKNOWN_ERROR", "获取评审列表时发生未知错误: " + e.getMessage());
        }
    }

    /**
     * 更新评审项状态
     * PUT /api/reviews/{id}/items/{itemId}?projectId=xxx
     * 使用正则表达式匹配完整的itemId，包括小数点
     */
    @PutMapping("/{id}/items/{itemId:.+}")
    public ResponseEntity<ApiResponse<ChecklistReview>> updateReviewItemStatus(
            @PathVariable String id,
            @PathVariable String itemId,
            @RequestParam String projectId,
            @RequestBody Map<String, String> request) {
        try {
            // 参数验证
            if (id == null || id.trim().isEmpty()) {
                return ResponseBuilder.badRequest("评审ID不能为空");
            }
            if (itemId == null || itemId.trim().isEmpty()) {
                return ResponseBuilder.badRequest("评审项ID不能为空");
            }
            if (projectId == null || projectId.trim().isEmpty()) {
                return ResponseBuilder.badRequest("项目ID不能为空");
            }

            String statusStr = request.get("status");
            if (statusStr == null || statusStr.trim().isEmpty()) {
                return ResponseBuilder.badRequest("状态不能为空");
            }

            // 不再限制状态值必须是枚举中的值，支持模板中定义的任意状态
            String statusValue = statusStr.trim();
            if (statusValue.isEmpty()) {
                return ResponseBuilder.badRequest("状态值不能为空");
            }

            String comment = request.get("comment");
            String reviewer = request.get("reviewer");

            ChecklistReview updatedReview = reviewService.updateReviewItemStatus(
                projectId.trim(), id.trim(), itemId.trim(), statusValue, comment, reviewer);

            return ResponseBuilder.success(updatedReview);
        } catch (IllegalArgumentException e) {
            return ResponseBuilder.validationError(e.getMessage());
        } catch (IOException e) {
            return ResponseBuilder.error("REVIEW_UPDATE_ERROR", "更新评审项失败: " + e.getMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("UNKNOWN_ERROR", "更新评审项时发生未知错误: " + e.getMessage());
        }
    }

    /**
     * 批量更新评审项状态
     * PUT /api/reviews/{id}/items/batch?projectId=xxx
     */
    @PutMapping("/{id}/items/batch")
    public ResponseEntity<ApiResponse<ChecklistReview>> batchUpdateReviewItemStatus(
            @PathVariable String id,
            @RequestParam String projectId,
            @RequestBody Map<String, Object> request) {
        try {
            // 参数验证
            if (id == null || id.trim().isEmpty()) {
                return ResponseBuilder.badRequest("评审ID不能为空");
            }
            if (projectId == null || projectId.trim().isEmpty()) {
                return ResponseBuilder.badRequest("项目ID不能为空");
            }

            @SuppressWarnings("unchecked")
            List<String> itemIds = (List<String>) request.get("itemIds");
            if (itemIds == null || itemIds.isEmpty()) {
                return ResponseBuilder.badRequest("评审项ID列表不能为空");
            }

            String statusStr = (String) request.get("status");
            if (statusStr == null || statusStr.trim().isEmpty()) {
                return ResponseBuilder.badRequest("状态不能为空");
            }

            String comment = (String) request.get("comment");
            String reviewer = (String) request.get("reviewer");

            ChecklistReview updatedReview = reviewService.batchUpdateReviewItemStatus(
                projectId.trim(), id.trim(), itemIds, statusStr.trim(), comment, reviewer);

            return ResponseBuilder.success(updatedReview);
        } catch (IllegalArgumentException e) {
            return ResponseBuilder.validationError(e.getMessage());
        } catch (IOException e) {
            return ResponseBuilder.error("REVIEW_BATCH_UPDATE_ERROR", "批量更新评审项失败: " + e.getMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("UNKNOWN_ERROR", "批量更新评审项时发生未知错误: " + e.getMessage());
        }
    }

    /**
     * 完成评审
     * PUT /api/reviews/{id}/complete?projectId=xxx
     */
    @PutMapping("/{id}/complete")
    public ResponseEntity<ApiResponse<ChecklistReview>> completeReview(
            @PathVariable String id,
            @RequestParam String projectId,
            @RequestBody(required = false) Map<String, Object> payload) {
        try {
            // 参数验证
            if (id == null || id.trim().isEmpty()) {
                return ResponseBuilder.badRequest("评审ID不能为空");
            }
            if (projectId == null || projectId.trim().isEmpty()) {
                return ResponseBuilder.badRequest("项目ID不能为空");
            }

            ChecklistReview completedReview = reviewService.completeReview(projectId.trim(), id.trim(), payload);
            return ResponseBuilder.success(completedReview);
        } catch (IllegalArgumentException e) {
            return ResponseBuilder.validationError(e.getMessage());
        } catch (IOException e) {
            return ResponseBuilder.error("REVIEW_COMPLETE_ERROR", "完成评审失败: " + e.getMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("UNKNOWN_ERROR", "完成评审时发生未知错误: " + e.getMessage());
        }
    }

    /**
     * 获取评审历史
     * GET /api/reviews/{id}/history?projectId=xxx
     */
    @GetMapping("/{id}/history")
    public ResponseEntity<ApiResponse<List<ReviewRecord>>> getReviewHistory(
            @PathVariable String id,
            @RequestParam String projectId) {
        try {
            // 参数验证
            if (id == null || id.trim().isEmpty()) {
                return ResponseBuilder.badRequest("评审ID不能为空");
            }
            if (projectId == null || projectId.trim().isEmpty()) {
                return ResponseBuilder.badRequest("项目ID不能为空");
            }

            List<ReviewRecord> history = reviewService.getReviewHistory(projectId.trim(), id.trim());
            return ResponseBuilder.success(history);
        } catch (IllegalArgumentException e) {
            return ResponseBuilder.validationError(e.getMessage());
        } catch (IOException e) {
            return ResponseBuilder.error("REVIEW_HISTORY_READ_ERROR", "读取评审历史失败: " + e.getMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("UNKNOWN_ERROR", "获取评审历史时发生未知错误: " + e.getMessage());
        }
    }

    /**
     * 取消评审
     * PUT /api/reviews/{id}/cancel?projectId=xxx
     */
    @PutMapping("/{id}/cancel")
    public ResponseEntity<ApiResponse<ChecklistReview>> cancelReview(
            @PathVariable String id,
            @RequestParam String projectId,
            @RequestBody(required = false) Map<String, String> request) {
        try {
            // 参数验证
            if (id == null || id.trim().isEmpty()) {
                return ResponseBuilder.badRequest("评审ID不能为空");
            }
            if (projectId == null || projectId.trim().isEmpty()) {
                return ResponseBuilder.badRequest("项目ID不能为空");
            }

            // 暂时使用删除方法来实现取消功能
            // TODO: 实现专门的取消评审方法
            reviewService.deleteReview(projectId.trim(), id.trim());

            // 返回一个表示已取消的评审对象
            ChecklistReview cancelledReview = new ChecklistReview();
            cancelledReview.setId(id.trim());
            cancelledReview.setStatus("CANCELLED");

            return ResponseBuilder.success(cancelledReview);
        } catch (IllegalArgumentException e) {
            return ResponseBuilder.validationError(e.getMessage());
        } catch (IOException e) {
            return ResponseBuilder.error("REVIEW_CANCEL_ERROR", "取消评审失败: " + e.getMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("UNKNOWN_ERROR", "取消评审时发生未知错误: " + e.getMessage());
        }
    }

    /**
     * 导出评审结果
     * GET /api/reviews/{id}/export?projectId=xxx&format=excel
     */
    @GetMapping("/{id}/export")
    public ResponseEntity<ApiResponse<String>> exportReview(
            @PathVariable String id,
            @RequestParam String projectId,
            @RequestParam(defaultValue = "excel") String format) {
        try {
            // 参数验证
            if (id == null || id.trim().isEmpty()) {
                return ResponseBuilder.badRequest("评审ID不能为空");
            }
            if (projectId == null || projectId.trim().isEmpty()) {
                return ResponseBuilder.badRequest("项目ID不能为空");
            }

            // TODO: 实现实际的导出功能
            // 暂时返回成功消息
            String message = String.format("评审 %s 的 %s 格式导出功能开发中", id, format);
            return ResponseBuilder.success(message);
        } catch (IllegalArgumentException e) {
            return ResponseBuilder.validationError(e.getMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("EXPORT_ERROR", "导出评审时发生未知错误: " + e.getMessage());
        }
    }

    /**
     * 获取评审统计信息
     * GET /api/reviews/statistics?projectId=xxx
     */
    @GetMapping("/statistics")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getReviewStatistics(@RequestParam String projectId) {
        try {
            // 参数验证
            if (projectId == null || projectId.trim().isEmpty()) {
                return ResponseBuilder.badRequest("项目ID不能为空");
            }

            Map<String, Object> statistics = reviewService.getReviewStatistics(projectId.trim());
            return ResponseBuilder.success(statistics);
        } catch (IllegalArgumentException e) {
            return ResponseBuilder.validationError(e.getMessage());
        } catch (IOException e) {
            return ResponseBuilder.error("STATISTICS_READ_ERROR", "读取统计信息失败: " + e.getMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("UNKNOWN_ERROR", "获取统计信息时发生未知错误: " + e.getMessage());
        }
    }

    /**
     * 自动保存评审进度
     * PUT /api/reviews/{id}/autosave?projectId=xxx
     */
    @PutMapping("/{id}/autosave")
    public ResponseEntity<ApiResponse<Void>> autoSaveReview(
            @PathVariable String id,
            @RequestParam String projectId,
            @RequestBody Map<String, Object> data) {
        try {
            // 参数验证
            if (id == null || id.trim().isEmpty()) {
                return ResponseBuilder.badRequest("评审ID不能为空");
            }
            if (projectId == null || projectId.trim().isEmpty()) {
                return ResponseBuilder.badRequest("项目ID不能为空");
            }

            // TODO: 实现实际的自动保存功能
            // 暂时直接返回成功，不做实际保存
            return ResponseBuilder.success(null);
        } catch (IllegalArgumentException e) {
            return ResponseBuilder.validationError(e.getMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("UNKNOWN_ERROR", "自动保存时发生未知错误: " + e.getMessage());
        }
    }

    /**
     * 获取检查单的模板版本信息
     * GET /api/reviews/{id}/template-version?projectId=xxx
     */
    @GetMapping("/{id}/template-version")
    public ResponseEntity<ApiResponse<ReviewTemplateVersion>> getReviewTemplateVersion(
            @PathVariable String id,
            @RequestParam String projectId) {
        try {
            // 参数验证
            if (id == null || id.trim().isEmpty()) {
                return ResponseBuilder.badRequest("评审ID不能为空");
            }
            if (projectId == null || projectId.trim().isEmpty()) {
                return ResponseBuilder.badRequest("项目ID不能为空");
            }

            ReviewTemplateVersion templateVersion = reviewService.getReviewTemplateVersion(projectId.trim(), id.trim());
            if (templateVersion != null) {
                return ResponseBuilder.success(templateVersion);
            } else {
                return ResponseBuilder.notFound("未找到模板版本信息");
            }
        } catch (IOException e) {
            return ResponseBuilder.error("TEMPLATE_VERSION_READ_ERROR", "读取模板版本信息失败: " + e.getMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("UNKNOWN_ERROR", "获取模板版本信息时发生未知错误: " + e.getMessage());
        }
    }

    /**
     * 获取检查单的模板快照
     * GET /api/reviews/{id}/snapshots/{snapshotId}?projectId=xxx
     */
    @GetMapping("/{id}/snapshots/{snapshotId}")
    public ResponseEntity<ApiResponse<TemplateSnapshot>> getReviewSnapshot(
            @PathVariable String id,
            @PathVariable String snapshotId,
            @RequestParam String projectId) {
        try {
            // 参数验证
            if (id == null || id.trim().isEmpty()) {
                return ResponseBuilder.badRequest("评审ID不能为空");
            }
            if (snapshotId == null || snapshotId.trim().isEmpty()) {
                return ResponseBuilder.badRequest("快照ID不能为空");
            }
            if (projectId == null || projectId.trim().isEmpty()) {
                return ResponseBuilder.badRequest("项目ID不能为空");
            }

            TemplateSnapshot snapshot = reviewService.getReviewSnapshot(projectId.trim(), id.trim(), snapshotId.trim());
            if (snapshot != null) {
                return ResponseBuilder.success(snapshot);
            } else {
                return ResponseBuilder.notFound("未找到快照信息");
            }
        } catch (IOException e) {
            return ResponseBuilder.error("SNAPSHOT_READ_ERROR", "读取快照信息失败: " + e.getMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("UNKNOWN_ERROR", "获取快照信息时发生未知错误: " + e.getMessage());
        }
    }
}